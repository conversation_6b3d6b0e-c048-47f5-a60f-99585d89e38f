services:
  # MongoDB Database
  mongodb:
    image: mongo:latest
    container_name: theramea-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: theramea
    volumes:
      - mongodb_data:/data/db
      - ./docker/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - theramea-network

  # Redis Cache
  redis:
    image: redis:alpine
    container_name: theramea-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - theramea-network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: theramea-backend
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      NODE_ENV: development
      PORT: 5000
      MONGODB_URI: *******************************************************************
      REDIS_URL: redis://redis:6379
      JWT_SECRET: theramea-super-secret-jwt-key-development-only
      JWT_REFRESH_SECRET: theramea-refresh-token-secret-development-only
      JWT_EXPIRES_IN: 7d
      JWT_REFRESH_EXPIRES_IN: 30d
      EMAIL_HOST: smtp.mailtrap.io
      EMAIL_PORT: 2525
      EMAIL_USER: your-mailtrap-username
      EMAIL_PASS: your-mailtrap-password
      EMAIL_FROM: <EMAIL>
      PAYSTACK_SECRET_KEY: sk_test_your-paystack-secret-key
      PAYSTACK_PUBLIC_KEY: pk_test_your-paystack-public-key
      DAILY_API_KEY: your-daily-api-key
      DAILY_DOMAIN: your-daily-domain
      FRONTEND_URL: http://localhost:3000
      SUPER_ADMIN_EMAIL: <EMAIL>
      SUPER_ADMIN_PASSWORD: admin123
      RATE_LIMIT_WINDOW_MS: 900000
      RATE_LIMIT_MAX_REQUESTS: 100
      SESSION_SECRET: theramea-session-secret-development-only
    depends_on:
      - mongodb
      - redis
    networks:
      - theramea-network

  # Frontend Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: theramea-frontend
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:5000/api
      NEXT_PUBLIC_SOCKET_URL: http://localhost:5000
      NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY: pk_test_your-paystack-public-key
      NEXT_PUBLIC_DAILY_DOMAIN: your-daily-domain
    depends_on:
      - backend
    networks:
      - theramea-network

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: theramea-nginx
    restart: unless-stopped
    ports:
      - "80:80"
    volumes:
      - ./docker/nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - frontend
      - backend
    networks:
      - theramea-network

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  backend_uploads:
    driver: local

networks:
  theramea-network:
    driver: bridge
