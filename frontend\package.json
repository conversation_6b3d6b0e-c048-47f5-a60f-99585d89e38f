{"name": "theramea-frontend", "version": "1.0.0", "private": true, "description": "Frontend for Theramea - Digital Counseling Platform", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@daily-co/daily-js": "^0.55.0", "@hookform/resolvers": "^3.3.2", "@tanstack/react-query": "^5.8.4", "axios": "^1.6.2", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.5", "lucide-react": "^0.294.0", "next": "^14.0.0", "next-themes": "^0.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "socket.io-client": "^4.7.4", "tailwind-merge": "^2.0.0", "zod": "^3.22.4", "zustand": "^4.5.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.8", "@types/node": "^20.9.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-config-next": "^14.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2"}}